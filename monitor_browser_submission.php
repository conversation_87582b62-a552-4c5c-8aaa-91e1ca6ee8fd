<?php

require_once 'vendor/autoload.php';

// Load Laravel environment
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\Course;
use App\Models\ZoomMeeting;
use App\Models\User;
use App\Models\Category;
use Illuminate\Support\Facades\Log;

echo "=== Browser Form Submission Monitor ===\n\n";

Log::info('=== Browser form submission monitoring started ===');

// Get initial state
$initialCourses = Course::count();
$initialMeetings = ZoomMeeting::count();

echo "Initial state:\n";
echo "- Courses: {$initialCourses}\n";
echo "- Zoom meetings: {$initialMeetings}\n\n";

// Check prerequisites
$categories = Category::where('is_active', true)->count();
$admins = User::where('role', 'admin')->count();

echo "Prerequisites:\n";
echo "- Active categories: {$categories}\n";
echo "- Admin users: {$admins}\n\n";

if ($categories == 0) {
    echo "⚠️  WARNING: No active categories found. This will cause form submission to fail.\n";
}

if ($admins == 0) {
    echo "⚠️  WARNING: No admin users found. This will cause form submission to fail.\n";
}

echo "Instructions for testing:\n";
echo "1. Open browser and go to: http://127.0.0.1:8000/admin-courses-create\n";
echo "2. Log in as admin if not already logged in\n";
echo "3. Fill out the course form with these test values:\n";
echo "   - Title: Browser Test Course " . date('Y-m-d H:i:s') . "\n";
echo "   - Category: Any available category\n";
echo "   - Instructor: Any available instructor\n";
echo "   - Status: Draft\n";
echo "   - Check 'This course has a live session'\n";
echo "   - Meeting Topic: Browser Test Meeting\n";
echo "   - Meeting Date: " . date('Y-m-d', strtotime('+7 days')) . "\n";
echo "   - Meeting Time: 16:00\n";
echo "4. Click 'Create Course'\n";
echo "5. Press Enter here after submitting the form\n\n";

// Wait for user input
echo "Press Enter after you've submitted the form in the browser...";
$handle = fopen("php://stdin", "r");
$line = fgets($handle);
fclose($handle);

echo "\nChecking results...\n";

// Get final state
$finalCourses = Course::count();
$finalMeetings = ZoomMeeting::count();

echo "\nFinal state:\n";
echo "- Courses: {$finalCourses} (+" . ($finalCourses - $initialCourses) . ")\n";
echo "- Zoom meetings: {$finalMeetings} (+" . ($finalMeetings - $initialMeetings) . ")\n\n";

// Check if any new courses were created
if ($finalCourses > $initialCourses) {
    echo "✅ Course(s) created successfully!\n";
    
    $newCourses = Course::where('id', '>', Course::orderBy('id', 'desc')->skip($finalCourses - $initialCourses)->first()->id ?? 0)->get();
    
    foreach ($newCourses as $course) {
        echo "\nNew course details:\n";
        echo "- ID: {$course->id}\n";
        echo "- Title: {$course->title}\n";
        echo "- Has live session: " . ($course->has_live_session ? 'Yes' : 'No') . "\n";
        echo "- Created at: {$course->created_at}\n";
        
        if ($course->has_live_session) {
            $meeting = $course->zoomMeetings()->first();
            if ($meeting) {
                echo "- Zoom meeting: ✅ {$meeting->topic} (ID: {$meeting->zoom_meeting_id})\n";
                echo "- Meeting start: {$meeting->start_time}\n";
            } else {
                echo "- Zoom meeting: ❌ Not created\n";
            }
        }
    }
} else {
    echo "❌ No new courses were created.\n";
    echo "\nPossible issues:\n";
    echo "1. Form validation failed\n";
    echo "2. Authentication/authorization failed\n";
    echo "3. CSRF token mismatch\n";
    echo "4. JavaScript prevented form submission\n";
    echo "5. Server error occurred\n";
}

// Check recent logs
echo "\n" . str_repeat("=", 50) . "\n";
echo "Recent Laravel logs:\n";
echo str_repeat("=", 50) . "\n";

$logFile = storage_path('logs/laravel.log');
if (file_exists($logFile)) {
    $logContent = file_get_contents($logFile);
    $logLines = explode("\n", $logContent);
    
    // Show last 20 lines or all lines if fewer
    $linesToShow = min(20, count($logLines));
    $recentLines = array_slice($logLines, -$linesToShow);
    
    foreach ($recentLines as $line) {
        if (!empty(trim($line))) {
            echo $line . "\n";
        }
    }
} else {
    echo "No log file found.\n";
}

echo "\n=== Monitoring Complete ===\n";
