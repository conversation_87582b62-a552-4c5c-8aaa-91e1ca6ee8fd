<?php

require_once 'vendor/autoload.php';

// Load Laravel environment
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\User;

echo "=== Admin Users Check ===\n\n";

$admins = User::where('role', 'admin')->get(['id', 'name', 'email']);

if ($admins->count() > 0) {
    echo "Available admin users:\n";
    foreach ($admins as $admin) {
        echo "- ID: {$admin->id}, Name: {$admin->name}, Email: {$admin->email}\n";
    }
    
    echo "\nTo log in, use one of these admin accounts.\n";
    echo "If you don't know the password, you can reset it or create a new admin user.\n";
} else {
    echo "No admin users found in the system.\n";
    echo "You need to create an admin user first.\n";
}

echo "\n=== Check Complete ===\n";
