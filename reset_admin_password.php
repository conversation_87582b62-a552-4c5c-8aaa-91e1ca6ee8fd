<?php

require_once 'vendor/autoload.php';

// Load Laravel environment
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\User;
use Illuminate\Support\Facades\Hash;

echo "=== Reset Admin Password ===\n\n";

$admin = User::where('email', '<EMAIL>')->first();

if ($admin) {
    // Set a simple password for testing
    $newPassword = 'admin123';
    $admin->password = Hash::make($newPassword);
    $admin->save();
    
    echo "✅ Password reset successfully!\n\n";
    echo "Admin Login Credentials:\n";
    echo "Email: {$admin->email}\n";
    echo "Password: {$newPassword}\n";
    echo "Name: {$admin->name}\n";
    echo "Role: {$admin->role}\n\n";
    echo "You can now log in at: http://127.0.0.1:8000/login\n";
} else {
    echo "❌ Admin user not found.\n";
}

echo "\n=== Reset Complete ===\n";
