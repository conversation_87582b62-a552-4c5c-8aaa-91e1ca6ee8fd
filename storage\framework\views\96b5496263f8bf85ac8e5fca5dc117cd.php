<?php $__env->startSection('content'); ?>
<div class="main-content">
    <div class="page-content">
        <div class="container-fluid">
            <!-- start page title -->
            <div class="row">
                <div class="col-12">
                    <div class="page-title-box d-flex align-items-center justify-content-between">
                        <h4 class="mb-0">Edit Course</h4>
                        <div class="page-title-right">
                            <ol class="breadcrumb m-0">
                                <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">Dashboard</a></li>
                                <li class="breadcrumb-item"><a href="<?php echo e(route('admin.courses.index')); ?>">Courses</a></li>
                                <li class="breadcrumb-item active">Edit Course</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
            <!-- end page title -->

            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <h4 class="card-title">Course Information</h4>
                            <p class="card-title-desc">Update the course details below.</p>

                            <?php if($errors->any()): ?>
                                <div class="alert alert-danger">
                                    <ul class="mb-0">
                                        <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <li><?php echo e($error); ?></li>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </ul>
                                </div>
                            <?php endif; ?>

                            <?php if(session('success')): ?>
                                <div class="alert alert-success">
                                    <?php echo e(session('success')); ?>

                                </div>
                            <?php endif; ?>

                            <form action="<?php echo e(route('admin.courses.update', $course->id)); ?>" method="POST" enctype="multipart/form-data" class="needs-validation" novalidate>
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('PUT'); ?>

                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="mb-3">
                                            <label class="form-label" for="title">Course Title</label>
                                            <input type="text" class="form-control" id="title" name="title"
                                                   placeholder="Enter course title" value="<?php echo e(old('title', $course->title)); ?>" required>
                                            <div class="invalid-feedback">
                                                Please provide a valid course title.
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label" for="status">Status</label>
                                            <select class="form-select" id="status" name="status" required>
                                                <option value="">Select Status</option>
                                                <option value="draft" <?php echo e(old('status', $course->status) == 'draft' ? 'selected' : ''); ?>>Draft</option>
                                                <option value="published" <?php echo e(old('status', $course->status) == 'published' ? 'selected' : ''); ?>>Published</option>
                                                <option value="archived" <?php echo e(old('status', $course->status) == 'archived' ? 'selected' : ''); ?>>Archived</option>
                                            </select>
                                            <div class="invalid-feedback">
                                                Please select a status.
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="mb-3">
                                            <label class="form-label" for="short_description">Short Description</label>
                                            <textarea class="form-control" id="short_description" name="short_description" rows="3"
                                                      placeholder="Brief description of the course"><?php echo e(old('short_description', $course->short_description)); ?></textarea>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label" for="category_id">Category</label>
                                            <select class="form-select" id="category_id" name="category_id" required>
                                                <option value="">Select Category</option>
                                                <?php $__currentLoopData = $categories ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($category->id); ?>" <?php echo e(old('category_id', $course->category_id) == $category->id ? 'selected' : ''); ?>>
                                                        <?php echo e($category->name); ?>

                                                    </option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                            <div class="invalid-feedback">
                                                Please select a category.
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label" for="instructor_id">Instructor</label>
                                            <select class="form-select" id="instructor_id" name="instructor_id" required>
                                                <option value="">Select Instructor</option>
                                                <?php $__currentLoopData = $instructors ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $instructor): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($instructor->id); ?>" <?php echo e(old('instructor_id', $course->instructor_id) == $instructor->id ? 'selected' : ''); ?>>
                                                        <?php echo e($instructor->name); ?>

                                                    </option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                            <div class="invalid-feedback">
                                                Please select an instructor.
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label" for="duration">Duration (hours)</label>
                                            <input type="number" class="form-control" id="duration" name="duration"
                                                   placeholder="0" value="<?php echo e(old('duration', $course->duration)); ?>" min="0" step="0.5">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label" for="difficulty_level">Difficulty Level</label>
                                            <select class="form-select" id="difficulty_level" name="difficulty_level">
                                                <option value="">Select Level</option>
                                                <option value="beginner" <?php echo e(old('difficulty_level', $course->difficulty_level) == 'beginner' ? 'selected' : ''); ?>>Beginner</option>
                                                <option value="intermediate" <?php echo e(old('difficulty_level', $course->difficulty_level) == 'intermediate' ? 'selected' : ''); ?>>Intermediate</option>
                                                <option value="advanced" <?php echo e(old('difficulty_level', $course->difficulty_level) == 'advanced' ? 'selected' : ''); ?>>Advanced</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label" for="language">Language</label>
                                            <select class="form-select" id="language" name="language">
                                                <option value="">Select Language</option>
                                                <option value="english" <?php echo e(old('language', $course->language) == 'english' ? 'selected' : ''); ?>>English</option>
                                                <option value="spanish" <?php echo e(old('language', $course->language) == 'spanish' ? 'selected' : ''); ?>>Spanish</option>
                                                <option value="french" <?php echo e(old('language', $course->language) == 'french' ? 'selected' : ''); ?>>French</option>
                                                <option value="german" <?php echo e(old('language', $course->language) == 'german' ? 'selected' : ''); ?>>German</option>
                                                <option value="other" <?php echo e(old('language', $course->language) == 'other' ? 'selected' : ''); ?>>Other</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label" for="price">Price ($)</label>
                                            <input type="number" class="form-control" id="price" name="price"
                                                   placeholder="0.00" value="<?php echo e(old('price', $course->price)); ?>" min="0" step="0.01">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label" for="discount_price">Discount Price ($)</label>
                                            <input type="number" class="form-control" id="discount_price" name="discount_price"
                                                   placeholder="0.00" value="<?php echo e(old('discount_price', $course->discount_price)); ?>" min="0" step="0.01">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <div class="form-check mt-4">
                                                <input type="checkbox" class="form-check-input" id="is_free" name="is_free" value="1"
                                                       <?php echo e(old('is_free', $course->is_free) ? 'checked' : ''); ?>>
                                                <label class="form-check-label" for="is_free">
                                                    Free Course
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input type="checkbox" class="form-check-input" id="is_featured" name="is_featured" value="1"
                                                       <?php echo e(old('is_featured', $course->is_featured) ? 'checked' : ''); ?>>
                                                <label class="form-check-label" for="is_featured">
                                                    Featured Course
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Live Session/Meeting Settings -->
                                <div class="row">
                                    <div class="col-md-12">
                                        <h5 class="mb-3">Live Session Settings</h5>
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input type="checkbox" class="form-check-input" id="has_live_session" name="has_live_session" value="1"
                                                       <?php echo e(old('has_live_session', $course->has_live_session) ? 'checked' : ''); ?>>
                                                <label class="form-check-label" for="has_live_session">
                                                    <strong>This course has a live session</strong>
                                                </label>
                                                <div class="form-text">Enable this to schedule a Zoom meeting for this course</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div id="meetingFields" style="display: none;">
                                    <?php
                                        $meeting = $course->zoom_meeting;
                                    ?>
                                    
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label class="form-label" for="meeting_topic">Meeting Topic</label>
                                                <input type="text" class="form-control" id="meeting_topic" name="meeting_topic"
                                                       placeholder="Enter meeting topic" value="<?php echo e(old('meeting_topic', $meeting->topic ?? '')); ?>">
                                                <div class="form-text">This will be the title of your Zoom meeting</div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label class="form-label" for="meeting_description">Meeting Description</label>
                                                <textarea class="form-control" id="meeting_description" name="meeting_description" rows="3"
                                                          placeholder="Describe what will be covered in the live session"><?php echo e(old('meeting_description', $meeting->description ?? '')); ?></textarea>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label" for="meeting_start_date">
                                                    Start Date <span class="text-danger">*</span>
                                                </label>
                                                <input type="date" class="form-control" id="meeting_start_date" name="meeting_start_date"
                                                       value="<?php echo e(old('meeting_start_date', $meeting ? $meeting->start_time->format('Y-m-d') : '')); ?>"
                                                       min="<?php echo e(date('Y-m-d')); ?>"
                                                       required>
                                                <div class="form-text">Select the date for the live session</div>
                                                <?php $__errorArgs = ['meeting_start_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <div class="text-danger small"><?php echo e($message); ?></div>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label" for="meeting_start_time">
                                                    Start Time <span class="text-danger">*</span>
                                                </label>
                                                <input type="time" class="form-control" id="meeting_start_time" name="meeting_start_time"
                                                       value="<?php echo e(old('meeting_start_time', $meeting ? $meeting->start_time->format('H:i') : '')); ?>"
                                                       required>
                                                <div class="form-text">Select the time for the live session</div>
                                                <?php $__errorArgs = ['meeting_start_time'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <div class="text-danger small"><?php echo e($message); ?></div>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label" for="meeting_duration">Duration (minutes)</label>
                                                <input type="number" class="form-control" id="meeting_duration" name="meeting_duration"
                                                       placeholder="60" value="<?php echo e(old('meeting_duration', $meeting->duration_minutes ?? 60)); ?>" min="15" max="480">
                                                <div class="form-text">Meeting duration in minutes (15-480)</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label" for="meeting_password">Meeting Password</label>
                                                <input type="text" class="form-control" id="meeting_password" name="meeting_password"
                                                       placeholder="Optional password" value="<?php echo e(old('meeting_password', $meeting->password ?? '')); ?>">
                                                <div class="form-text">Leave empty for no password</div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label" for="minimum_attendance_minutes">Minimum Attendance (minutes)</label>
                                                <input type="number" class="form-control" id="minimum_attendance_minutes" name="minimum_attendance_minutes"
                                                       placeholder="45" value="<?php echo e(old('minimum_attendance_minutes', $course->minimum_attendance_minutes ?? 45)); ?>" min="1">
                                                <div class="form-text">Minimum time students must attend to qualify</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <div class="form-check mt-4">
                                                    <input type="checkbox" class="form-check-input" id="meeting_required_for_completion" name="meeting_required_for_completion" value="1"
                                                           <?php echo e(old('meeting_required_for_completion', $course->meeting_required_for_completion) ? 'checked' : ''); ?>>
                                                    <label class="form-check-label" for="meeting_required_for_completion">
                                                        Required for course completion
                                                    </label>
                                                    <div class="form-text">Students must attend the meeting to complete the course</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <?php if($meeting): ?>
                                        <div class="alert alert-info">
                                            <i class="uil-info-circle me-2"></i>
                                            <strong>Meeting Status:</strong> <?php echo e(ucfirst($meeting->status)); ?>

                                            <?php if($meeting->zoom_meeting_id): ?>
                                                <br><strong>Zoom Meeting ID:</strong> <?php echo e($meeting->zoom_meeting_id); ?>

                                            <?php endif; ?>
                                            <?php if($meeting->join_url): ?>
                                                <br><strong>Join URL:</strong> <a href="<?php echo e($meeting->join_url); ?>" target="_blank"><?php echo e($meeting->join_url); ?></a>
                                            <?php endif; ?>
                                        </div>
                                    <?php else: ?>
                                        <div class="alert alert-info">
                                            <i class="uil-info-circle me-2"></i>
                                            <strong>Note:</strong> The Zoom meeting will be created/updated when you save the course.
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label" for="thumbnail">Course Thumbnail</label>
                                            <input type="file" class="form-control" id="thumbnail" name="thumbnail" accept="image/*">
                                            <div class="form-text">Upload a new thumbnail (optional)</div>
                                            <?php if($course->thumbnail): ?>
                                                <div class="mt-2">
                                                    <small class="text-muted">Current: <?php echo e(basename($course->thumbnail)); ?></small>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label" for="preview_video">Preview Video URL</label>
                                            <input type="url" class="form-control" id="preview_video" name="preview_video"
                                                   placeholder="https://youtube.com/watch?v=..." value="<?php echo e(old('preview_video', $course->preview_video)); ?>">
                                        </div>
                                    </div>
                                </div>

                                <div class="d-flex justify-content-end">
                                    <a href="<?php echo e(route('admin.courses.index')); ?>" class="btn btn-secondary me-2">
                                        <i class="uil-arrow-left me-1"></i> Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="uil-check me-1"></i> Update Course
                                    </button>
                                    <button type="button" class="btn btn-danger ms-2" onclick="deleteCourse()">
                                        <i class="uil-trash me-1"></i> Delete
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer class="footer">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-6">
                    <script>document.write(new Date().getFullYear())</script> © Lernovate.
                </div>
                <div class="col-sm-6">
                    <div class="text-sm-end d-none d-sm-block">
                        Crafted with <i class="mdi mdi-heart text-danger"></i> by Lernovate Team
                    </div>
                </div>
            </div>
        </div>
    </footer>
</div>


<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this course? This action cannot be undone and will remove all associated data.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form action="<?php echo e(route('admin.courses.destroy', $course->id)); ?>" method="POST" style="display: inline;">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('DELETE'); ?>
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
    // Auto-generate slug from course title
    document.getElementById('title').addEventListener('input', function() {
        const title = this.value;
        const slug = title.toLowerCase()
            .replace(/[^a-z0-9 -]/g, '') // Remove invalid chars
            .replace(/\s+/g, '-') // Replace spaces with -
            .replace(/-+/g, '-') // Replace multiple - with single -
            .trim('-'); // Trim - from start and end

        // If there's a slug field, update it
        const slugField = document.getElementById('slug');
        if (slugField) {
            slugField.value = slug;
        }
    });

    // Toggle pricing based on free course checkbox
    document.getElementById('is_free').addEventListener('change', function() {
        const priceInput = document.getElementById('price');
        const discountPriceInput = document.getElementById('discount_price');

        if (this.checked) {
            priceInput.disabled = true;
            discountPriceInput.disabled = true;
            priceInput.value = '0';
            discountPriceInput.value = '0';
        } else {
            priceInput.disabled = false;
            discountPriceInput.disabled = false;
        }
    });

    // Delete course function
    function deleteCourse() {
        const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
        deleteModal.show();
    }

    // Bootstrap form validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    if (form.checkValidity() === false) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();

    // Toggle meeting fields based on live session checkbox
    document.getElementById('has_live_session').addEventListener('change', function() {
        const meetingFields = document.getElementById('meetingFields');
        const meetingInputs = meetingFields.querySelectorAll('input, textarea, select');
        const meetingStartDate = document.getElementById('meeting_start_date');
        const meetingStartTime = document.getElementById('meeting_start_time');

        if (this.checked) {
            meetingFields.style.display = 'block';
            // Make date and time fields required
            meetingStartDate.setAttribute('required', 'required');
            meetingStartTime.setAttribute('required', 'required');

            // Auto-populate meeting topic from course title if empty
            const courseTitle = document.getElementById('title').value;
            const meetingTopic = document.getElementById('meeting_topic');
            if (courseTitle && !meetingTopic.value) {
                meetingTopic.value = courseTitle + ' - Live Session';
            }
        } else {
            meetingFields.style.display = 'none';
            // Remove required attributes
            meetingStartDate.removeAttribute('required');
            meetingStartTime.removeAttribute('required');
        }
    });

    // Auto-update meeting topic when course title changes (only if meeting topic is empty or contains "Live Session")
    document.getElementById('title').addEventListener('input', function() {
        const hasLiveSession = document.getElementById('has_live_session').checked;
        const meetingTopic = document.getElementById('meeting_topic');
        
        if (hasLiveSession && this.value && (!meetingTopic.value || meetingTopic.value.includes('Live Session'))) {
            meetingTopic.value = this.value + ' - Live Session';
        }
    });

    // Initialize on page load
    document.addEventListener('DOMContentLoaded', function() {
        // Trigger free course checkbox change to set initial state
        document.getElementById('is_free').dispatchEvent(new Event('change'));
        
        // Trigger live session checkbox change to set initial state
        document.getElementById('has_live_session').dispatchEvent(new Event('change'));
    });
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Laravel-Apps\lernovate\resources\views/admin/courses/edit.blade.php ENDPATH**/ ?>